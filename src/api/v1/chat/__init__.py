from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel


from api.services.chat_service import create_chat_service
from core.security import get_tenant_info
from models.user import UserTenantDB
from utils import setup_colored_logging, log_info, log_error, log_success
from langgraph.checkpoint.mongodb import MongoDBSaver
from core.database import get_db_from_tenant_id

# Setup logging
setup_colored_logging()

router = APIRouter(tags=["Chat"])


class ChatRequest(BaseModel):
    message: str


class ToolUsed(BaseModel):
    name: str
    description: str
    input: dict = {}
    output: str = ""

class ChatResponse(BaseModel):
    response: str
    thread_id: str
    user_id: str
    tools_used: list[ToolUsed] = []


@router.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Chat endpoint that uses ChatService with current user context
    The user ID from the token is used as the thread ID for conversation continuity
    """
    try:
        # Create chat service with current user context (includes vector store, agents, tools)
        chat_service = create_chat_service(current_user)

        # Use user ID as thread ID for conversation continuity per user
        thread_id = str(current_user.user.id)
        user_id = str(current_user.user.id)

        log_info(f"Chat request from user {current_user.user.username} (ID: {user_id}, tenant: {current_user.tenant_id}): {chat_request.message[:50]}...")

        # Get response from chat service (automatically uses current user's vector store and agents)
        agent_response = chat_service.chat(chat_request.message, thread_id)

        log_success(f"Chat response generated for user {current_user.user.username}")

        # Handle both old string format and new dict format for backward compatibility
        if isinstance(agent_response, dict):
            response_text = agent_response.get("response", "")
            tools_used = [ToolUsed(**tool) for tool in agent_response.get("tools_used", [])]
        else:
            response_text = agent_response
            tools_used = []

        return ChatResponse(
            response=response_text,
            thread_id=thread_id,
            user_id=user_id,
            tools_used=tools_used
        )
        
    except Exception as e:
        log_error(f"Chat failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to process chat request"
        )


@router.delete("/chat/clear")
async def clear_conversation(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Clear conversation history for the current user
    """
    try:
        # Get tenant database and MongoDB memory
        tenant_db = get_db_from_tenant_id(current_user.tenant_id)
        mongo_client = tenant_db.client

        memory = MongoDBSaver(
            client=mongo_client,
            db_name=tenant_db.name,
            collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
        )

        # Clear conversation for this user's thread
        thread_id = str(current_user.user.id)
        config = {"configurable": {"thread_id": thread_id}}

        # Delete the conversation checkpoint
        try:
            # Get the collection directly and delete the document
            checkpoint_collection = mongo_client[tenant_db.name][f"conversation_checkpoints_{current_user.tenant_id}"]
            result = checkpoint_collection.delete_many({"thread_id": thread_id})

            log_success(f"Cleared conversation for user {current_user.user.username} (deleted {result.deleted_count} documents)")

            return {
                "status": "success",
                "message": f"Conversation history cleared for user {current_user.user.username}",
                "deleted_count": result.deleted_count
            }

        except Exception as e:
            log_error(f"Error clearing conversation for user {current_user.user.username}", e)
            raise HTTPException(
                status_code=500,
                detail="Failed to clear conversation history"
            )

    except Exception as e:
        log_error(f"Clear conversation failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to clear conversation"
        )


@router.get("/chat/health")
async def chat_health_check():
    """
    Health check endpoint for chat service
    """
    return {
        "status": "healthy",
        "service": "chat",
        "message": "Chat service is running with ChatService architecture"
    }
