"""
Simple Booking Agent - Clean booking agent for ChatService
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.mongodb import MongoDBSaver
from models.user import UserTenantDB
from core.database import get_db_from_tenant_id
from api.services.booking_service import BookingService
from models.booking import BookingCreate
from utils.conversation_memory_extractor import conversation_memory_extractor
import os
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Note: Using shared MongoDB memory instead of separate sessions


class SimpleBookingAgent:
    """
    Simple Booking Agent - Clean booking workflow
    """
    
    def __init__(self, current_user : UserTenantDB=None):
        """Initialize the booking agent"""
        self.current_user = current_user

        # LLM for context understanding
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )

        # Access to search tools through current user
        self.vector_store_manager = current_user.vector_store_manager if current_user else None

        # Initialize booking service for database operations
        if current_user and current_user.tenant_id:
            self.booking_service = BookingService(current_user.tenant_id)
        else:
            self.booking_service = None

        # Use the same MongoDB memory as main agent
        if current_user and current_user.tenant_id:
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)
            mongo_client = tenant_db.client
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )
            logger.info(f"✅ Booking agent using shared MongoDB memory for tenant: {current_user.tenant_id}")
        else:
            self.memory = None
            logger.warning("⚠️ No memory available for booking agent")

        logger.info("✅ Simple Booking Agent initialized")

    def handle_booking_request(self, user_message: str, thread_id: str) -> str:
        """Handle booking request"""
        try:
            logger.info(f"📅 Booking request: {user_message}")

            # Get conversation context from shared memory
            conversation_context = self._get_conversation_context(thread_id)
            logger.info(f"🔍 Booking agent conversation context: {conversation_context[:200]}...")

            # Extract user information from conversation context
            user_info = self._extract_user_info(conversation_context)

            # First, try to search for courses if user mentions a course name
            search_results = ""
            if self.vector_store_manager and any(keyword in user_message.lower() for keyword in ['course', 'class', 'bridge', 'see', 'bbs', 'csit', 'ielts', 'german', 'korean']):
                try:
                    search_results = self.vector_store_manager.search_products(user_message)
                    logger.info(f"🔍 Found course search results for booking request")
                except Exception as e:
                    logger.warning(f"⚠️ Could not search for courses: {e}")

            # Use LLM to understand the booking request with search context
            system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

Help users book courses by:
1. Understanding what course they want to book (use search results if available)
2. NEVER ask for information that was already provided in conversation history
3. Offering available time slots
4. Confirming the booking and saving it to database

Be friendly and guide them through the process step by step.
Use "Namaste" as greeting when appropriate.

IMPORTANT: You have access to persistent conversation memory. If the user has already provided their name,
email, or phone number in ANY previous message, DO NOT ask for it again. Use what they already provided.

User Information Already Provided:
{user_info}

{search_context}

Conversation History Context:
{conversation_context}

Respond helpfully to their request."""

            search_context = f"Available courses from search:\n{search_results}\n" if search_results else "No specific course search results available."

            response = self.llm.invoke([
                SystemMessage(content=system_prompt.format(
                    user_info=user_info,
                    search_context=search_context,
                    conversation_context=conversation_context
                )),
                HumanMessage(content=f"User message: {user_message}")
            ])
            
            result = response.content
            logger.info(f"✅ Booking response generated")
            return result
            
        except Exception as e:
            error_msg = f"Error handling booking: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble processing your booking request. Please try again or contact our support team."

    def _get_conversation_context(self, thread_id: str) -> str:
        """Get conversation context from shared MongoDB memory"""
        if not self.memory:
            return "No conversation history available."

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Extract user information from conversation history
                context_info = []
                for msg in messages[-10:]:  # Last 10 messages for context
                    if hasattr(msg, 'content'):
                        content = msg.content.lower()
                        # Look for user information patterns
                        if any(keyword in content for keyword in ['name is', 'i am', 'my name']):
                            context_info.append(f"User message: {msg.content}")
                        elif any(keyword in content for keyword in ['phone', 'number', 'email']):
                            context_info.append(f"User provided: {msg.content}")

                return "\n".join(context_info) if context_info else "No specific user information found in conversation."
            else:
                return "No conversation history found."

        except Exception as e:
            logger.warning(f"Could not retrieve conversation context: {e}")
            return "Error retrieving conversation history."

    def _extract_user_info(self, conversation_context: str) -> str:
        """Extract user information from conversation context"""
        user_info = []

        # Look for name patterns
        if any(keyword in conversation_context.lower() for keyword in ['name is', 'i am', 'my name']):
            user_info.append("✅ User name has been provided in conversation")

        # Look for email patterns
        if any(keyword in conversation_context.lower() for keyword in ['email', '@']):
            user_info.append("✅ User email has been provided in conversation")

        # Look for phone patterns
        if any(keyword in conversation_context.lower() for keyword in ['phone', 'number', 'contact']):
            user_info.append("✅ User phone number has been provided in conversation")

        if not user_info:
            return "❌ No user information found in conversation history"

        return "\n".join(user_info)

    def has_pending_booking(self, thread_id: str) -> Dict[str, Any]:
        """Check if user has a pending/incomplete booking from conversation history"""
        # For now, return no pending bookings since we're using shared memory
        # This could be enhanced to check conversation history for incomplete bookings
        return {"has_pending": False}

    def get_pending_booking_reminder(self, thread_id: str) -> str:
        """Get reminder message for pending booking"""
        # Since we're using shared memory, no separate booking reminders needed
        return ""
