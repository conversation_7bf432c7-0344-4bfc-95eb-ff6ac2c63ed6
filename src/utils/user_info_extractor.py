"""
Dynamic User Information Extractor
Extracts user information (name, email, phone) from conversation history using regex patterns
"""

import re
import logging
from typing import Dict, Optional, List, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class UserInfo:
    """Structured user information"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Optional[str]]:
        return {
            "name": self.name,
            "email": self.email,
            "phone": self.phone
        }
    
    def is_complete(self) -> bool:
        """Check if all required fields are present"""
        return all([self.name, self.email, self.phone])
    
    def missing_fields(self) -> List[str]:
        """Get list of missing required fields"""
        missing = []
        if not self.name:
            missing.append("name")
        if not self.email:
            missing.append("email")
        if not self.phone:
            missing.append("phone")
        return missing


class UserInfoExtractor:
    """Extract user information from conversation history using dynamic patterns"""
    
    def __init__(self):
        # Email regex pattern
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            re.IGNORECASE
        )
        
        # Phone number patterns (various formats)
        self.phone_patterns = [
            re.compile(r'\b(?:\+977[-.\s]?)?(?:98|97)\d{8}\b'),  # Nepali mobile
            re.compile(r'\b\+?977[-.\s]?\d{7,10}\b'),  # Nepal landline/mobile
            re.compile(r'\b\d{10}\b'),  # 10 digit numbers
            re.compile(r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b'),  # XXX-XXX-XXXX format
            re.compile(r'\b\+\d{1,3}[-.\s]?\d{6,14}\b'),  # International format
        ]
        
        # Name extraction patterns
        self.name_patterns = [
            re.compile(r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]{2,30})', re.IGNORECASE),
            re.compile(r'name:\s*([A-Za-z\s]{2,30})', re.IGNORECASE),
            re.compile(r'(?:this is|here is)\s+([A-Za-z\s]{2,30})', re.IGNORECASE),
        ]
        
        # Context patterns to help identify user information
        self.context_patterns = {
            'name': [
                r'(?:my name|i am|i\'m|call me|name is)',
                r'(?:name:|full name:)',
                r'(?:this is|here is)'
            ],
            'email': [
                r'(?:my email|email is|email:|e-mail)',
                r'(?:contact me at|reach me at)',
                r'(?:send to|mail to)'
            ],
            'phone': [
                r'(?:my phone|phone is|phone:|number is)',
                r'(?:call me at|contact number|mobile)',
                r'(?:my number|phone number)'
            ]
        }
    
    def extract_from_conversation(self, conversation_history: List[Dict[str, Any]]) -> UserInfo:
        """
        Extract user information from conversation history
        
        Args:
            conversation_history: List of conversation messages with 'content' and 'type' fields
            
        Returns:
            UserInfo object with extracted information
        """
        user_info = UserInfo()
        
        # Process all messages to find user information
        for message in conversation_history:
            if not isinstance(message, dict):
                continue
                
            content = message.get('content', '')
            message_type = message.get('type', '')
            
            # Only extract from human/user messages
            if message_type.lower() in ['human', 'user'] and content:
                self._extract_from_text(content, user_info)
        
        logger.info(f"Extracted user info: name={bool(user_info.name)}, email={bool(user_info.email)}, phone={bool(user_info.phone)}")
        return user_info
    
    def extract_from_text(self, text: str) -> UserInfo:
        """
        Extract user information from a single text string
        
        Args:
            text: Text to extract information from
            
        Returns:
            UserInfo object with extracted information
        """
        user_info = UserInfo()
        self._extract_from_text(text, user_info)
        return user_info
    
    def _extract_from_text(self, text: str, user_info: UserInfo) -> None:
        """Internal method to extract information from text"""
        if not text:
            return
            
        # Extract email
        if not user_info.email:
            email_match = self.email_pattern.search(text)
            if email_match:
                user_info.email = email_match.group().strip()
                logger.debug(f"Extracted email: {user_info.email}")
        
        # Extract phone
        if not user_info.phone:
            for pattern in self.phone_patterns:
                phone_match = pattern.search(text)
                if phone_match:
                    # Clean up phone number
                    phone = re.sub(r'[-.\s]', '', phone_match.group())
                    user_info.phone = phone.strip()
                    logger.debug(f"Extracted phone: {user_info.phone}")
                    break
        
        # Extract name
        if not user_info.name:
            for pattern in self.name_patterns:
                name_match = pattern.search(text)
                if name_match:
                    name = name_match.group(1).strip()
                    # Validate name (should be reasonable length and contain letters)
                    if 2 <= len(name) <= 30 and re.match(r'^[A-Za-z\s]+$', name):
                        user_info.name = name.title()  # Capitalize properly
                        logger.debug(f"Extracted name: {user_info.name}")
                        break
    
    def extract_from_mongodb_messages(self, messages: List[Any]) -> UserInfo:
        """
        Extract user information from MongoDB message objects
        
        Args:
            messages: List of message objects from MongoDB
            
        Returns:
            UserInfo object with extracted information
        """
        user_info = UserInfo()
        
        for msg in messages:
            try:
                # Handle different message formats
                if hasattr(msg, 'content'):
                    content = msg.content
                    msg_type = getattr(msg, 'type', 'unknown')
                elif isinstance(msg, dict):
                    content = msg.get('content', '')
                    msg_type = msg.get('type', 'unknown')
                else:
                    continue
                
                # Only process human/user messages
                if msg_type.lower() in ['human', 'user', 'humanmessage'] and content:
                    self._extract_from_text(str(content), user_info)
                    
            except Exception as e:
                logger.warning(f"Error processing message: {e}")
                continue
        
        return user_info
    
    def validate_extracted_info(self, user_info: UserInfo) -> Dict[str, bool]:
        """
        Validate extracted user information
        
        Args:
            user_info: UserInfo object to validate
            
        Returns:
            Dictionary with validation results for each field
        """
        validation = {
            'name': False,
            'email': False,
            'phone': False
        }
        
        # Validate name
        if user_info.name:
            name = user_info.name.strip()
            if 2 <= len(name) <= 50 and re.match(r'^[A-Za-z\s]+$', name):
                validation['name'] = True
        
        # Validate email
        if user_info.email:
            if self.email_pattern.match(user_info.email):
                validation['email'] = True
        
        # Validate phone
        if user_info.phone:
            # Remove any formatting and check if it's a valid number
            clean_phone = re.sub(r'[-.\s+()]', '', user_info.phone)
            if clean_phone.isdigit() and 7 <= len(clean_phone) <= 15:
                validation['phone'] = True
        
        return validation


# Global instance for easy import
user_info_extractor = UserInfoExtractor()
