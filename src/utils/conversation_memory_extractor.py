"""
LLM-based Conversation Memory Extractor
Uses structured LLM extraction to get user information from conversation history
Based on Instructor patterns for reliable structured data extraction
"""

import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import SystemMessage, HumanMessage
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class UserInformation(BaseModel):
    """Structured user information extracted from conversation"""
    name: Optional[str] = Field(None, description="User's full name if mentioned")
    email: Optional[str] = Field(None, description="User's email address if provided")
    phone: Optional[str] = Field(None, description="User's phone number if provided")
    confidence: float = Field(0.0, description="Confidence score for extracted information (0-1)")
    
    def is_complete(self) -> bool:
        """Check if all required fields are present"""
        return all([self.name, self.email, self.phone])
    
    def missing_fields(self) -> List[str]:
        """Get list of missing required fields"""
        missing = []
        if not self.name:
            missing.append("name")
        if not self.email:
            missing.append("email")
        if not self.phone:
            missing.append("phone")
        return missing


class ConversationMemoryExtractor:
    """Extract user information from conversation history using LLM"""
    
    def __init__(self):
        """Initialize the extractor with LLM"""
        try:
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                temperature=0.1,  # Low temperature for consistent extraction
                google_api_key=os.getenv("GOOGLE_API_KEY")
            )
            logger.info("✅ Conversation memory extractor initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            self.llm = None
    
    def extract_user_info_from_messages(self, messages: List[Any]) -> UserInformation:
        """
        Extract user information from conversation messages using LLM
        
        Args:
            messages: List of conversation messages
            
        Returns:
            UserInformation object with extracted data
        """
        if not self.llm:
            logger.warning("LLM not available, returning empty user info")
            return UserInformation()
        
        try:
            # Convert messages to text for analysis
            conversation_text = self._messages_to_text(messages)
            
            if not conversation_text.strip():
                return UserInformation()
            
            # Create extraction prompt
            system_prompt = """You are an expert at extracting user information from conversations.
            
Your task is to analyze the conversation and extract:
1. User's name (full name if available)
2. User's email address
3. User's phone number

Rules:
- Only extract information that is explicitly mentioned by the user
- Do not make assumptions or generate fake information
- If information is not clearly stated, leave the field as null
- Provide a confidence score (0-1) based on how certain you are about the extracted information
- Focus on information provided by the user (human messages), not AI responses

Return the information in the specified JSON format."""

            user_prompt = f"""Analyze this conversation and extract user information:

{conversation_text}

Extract the user's name, email, and phone number if they are mentioned in the conversation."""

            # Use LLM to extract structured information
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # For now, we'll parse the response manually since we don't have Instructor set up
            response = self.llm.invoke(messages)
            
            # Parse the response to extract user information
            return self._parse_llm_response(response.content, conversation_text)
            
        except Exception as e:
            logger.error(f"Error extracting user info: {e}")
            return UserInformation()
    
    def _messages_to_text(self, messages: List[Any]) -> str:
        """Convert messages to readable text for LLM analysis"""
        conversation_parts = []
        
        for msg in messages:
            try:
                # Handle different message formats
                if hasattr(msg, 'content'):
                    content = str(msg.content)
                    msg_type = getattr(msg, 'type', 'unknown')
                elif isinstance(msg, dict):
                    content = str(msg.get('content', ''))
                    msg_type = msg.get('type', 'unknown')
                else:
                    continue
                
                # Only include human/user messages for user info extraction
                if msg_type.lower() in ['human', 'user', 'humanmessage'] and content.strip():
                    conversation_parts.append(f"User: {content}")
                    
            except Exception as e:
                logger.warning(f"Error processing message: {e}")
                continue
        
        return "\n".join(conversation_parts)
    
    def _parse_llm_response(self, response_text: str, original_text: str) -> UserInformation:
        """Parse LLM response to extract user information"""
        try:
            # Simple parsing logic - look for common patterns in LLM responses
            user_info = UserInformation()
            
            response_lower = response_text.lower()
            original_lower = original_text.lower()
            
            # Extract name
            if 'name' in response_lower:
                # Look for name patterns in original text
                import re
                name_patterns = [
                    r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]{2,30})',
                    r'name:\s*([A-Za-z\s]{2,30})',
                    r'(?:this is|here is)\s+([A-Za-z\s]{2,30})'
                ]
                
                for pattern in name_patterns:
                    match = re.search(pattern, original_text, re.IGNORECASE)
                    if match:
                        name = match.group(1).strip()
                        if 2 <= len(name) <= 30 and re.match(r'^[A-Za-z\s]+$', name):
                            user_info.name = name.title()
                            break
            
            # Extract email
            if 'email' in response_lower or '@' in original_text:
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                email_match = re.search(email_pattern, original_text, re.IGNORECASE)
                if email_match:
                    user_info.email = email_match.group().strip()
            
            # Extract phone
            if 'phone' in response_lower or 'number' in response_lower:
                phone_patterns = [
                    r'\b(?:\+977[-.\s]?)?(?:98|97)\d{8}\b',  # Nepali mobile
                    r'\b\+?977[-.\s]?\d{7,10}\b',  # Nepal landline/mobile
                    r'\b\d{10}\b',  # 10 digit numbers
                    r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b',  # XXX-XXX-XXXX format
                ]
                
                for pattern in phone_patterns:
                    phone_match = re.search(pattern, original_text)
                    if phone_match:
                        phone = re.sub(r'[-.\s]', '', phone_match.group())
                        user_info.phone = phone.strip()
                        break
            
            # Set confidence based on how much information was found
            found_fields = sum([bool(user_info.name), bool(user_info.email), bool(user_info.phone)])
            user_info.confidence = found_fields / 3.0
            
            logger.info(f"Extracted user info: name={bool(user_info.name)}, email={bool(user_info.email)}, phone={bool(user_info.phone)}, confidence={user_info.confidence}")
            return user_info
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return UserInformation()


# Global instance for easy import
conversation_memory_extractor = ConversationMemoryExtractor()
